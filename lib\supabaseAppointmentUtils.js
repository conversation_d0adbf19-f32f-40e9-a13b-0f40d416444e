import {
  supabase,
  supabaseAdmin,
  TABLES,
  APPOINTMENT_STATUS,
  handleSupabaseError,
  convertSupabaseToJsonFormat,
  validateAppointmentData
} from './supabase.js';

/**
 * Read all appointments from Supabase
 * @returns {Array} Array of appointments in JSON format
 */
export const readAppointments = async () => {
  try {
    const { data, error } = await supabase
      .from(TABLES.APPOINTMENTS)
      .select('*')
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error reading appointments:', error);
      throw new Error(`Failed to read appointments: ${error.message}`);
    }

    // Convert to JSON format for API compatibility
    return data.map(convertSupabaseToJsonFormat);
  } catch (error) {
    console.error('Error reading appointments:', error);
    return [];
  }
};

/**
 * Add a new appointment to Supabase
 * @param {Object} appointmentData - Appointment data
 * @returns {Object} The saved appointment with ID in JSON format
 */
export const addAppointment = async (appointmentData) => {
  try {
    // Validate appointment data
    validateAppointmentData(appointmentData);

    // Check if time slot is available
    const isAvailable = await isTimeSlotAvailable(
      appointmentData.dataAppuntamento, 
      appointmentData.orario
    );

    if (!isAvailable) {
      throw new Error('TIME_SLOT_UNAVAILABLE');
    }

    // Find employee ID if operatore is specified
    let employeeId = null;
    if (appointmentData.operatore && appointmentData.operatore !== 'Qualsiasi') {
      try {
        const { data: employees } = await supabase
          .from(TABLES.EMPLOYEES)
          .select('id')
          .eq('name', appointmentData.operatore)
          .eq('is_active', true)
          .limit(1);

        if (employees && employees.length > 0) {
          employeeId = employees[0].id;
        }
      } catch (error) {
        console.warn('Could not find employee ID for:', appointmentData.operatore, error);
      }
    }

    // Prepare data for Supabase (convert field names)
    const supabaseData = {
      nome: appointmentData.nome,
      cognome: appointmentData.cognome,
      telefono: appointmentData.telefono && appointmentData.telefono.trim() !== ''
        ? appointmentData.telefono
        : 'Non Inserito',
      email: appointmentData.email && appointmentData.email.trim() !== ''
        ? appointmentData.email
        : 'Non Inserito',
      servizio: appointmentData.servizio,
      prestazione: appointmentData.prestazione || null,
      operatore: appointmentData.operatore || 'Qualsiasi',
      note_aggiuntive: appointmentData.noteAggiuntive || null,
      data_appuntamento: appointmentData.dataAppuntamento,
      orario: appointmentData.orario,
      status: APPOINTMENT_STATUS.CONFIRMED,
      employee_id: employeeId
    };

    const { data, error } = await supabase
      .from(TABLES.APPOINTMENTS)
      .insert([supabaseData])
      .select()
      .single();

    if (error) {
      if (error.code === '23505') {
        throw new Error('TIME_SLOT_UNAVAILABLE');
      }
      console.error('Error adding appointment:', error);
      throw new Error(`Failed to add appointment: ${error.message}`);
    }

    // Convert back to JSON format
    return convertSupabaseToJsonFormat(data);
  } catch (error) {
    console.error('Error adding appointment:', error);
    throw error;
  }
};

/**
 * Update appointment status
 * @param {string} appointmentId - Appointment ID
 * @param {string} status - New status
 * @returns {boolean} Success status
 */
export const updateAppointmentStatus = async (appointmentId, status) => {
  try {
    console.log(`updateAppointmentStatus called with ID: ${appointmentId} (type: ${typeof appointmentId}), status: ${status}`);

    if (!Object.values(APPOINTMENT_STATUS).includes(status)) {
      throw new Error('Invalid status');
    }

    // CRITICAL FIX: Use the same approach as readAppointments to find the appointment
    console.log('🔍 Finding appointment using same method as readAppointments...');

    // Get all appointments using the same method as the UI
    const allAppointments = await readAppointments();
    console.log(`Found ${allAppointments.length} total appointments from readAppointments()`);

    // Find the appointment by ID (handle both string and number)
    const targetAppointment = allAppointments.find(apt =>
      apt.id == appointmentId || apt.id === appointmentId ||
      apt.id === String(appointmentId) || apt.id === parseInt(appointmentId, 10)
    );

    if (!targetAppointment) {
      console.error(`Appointment with ID ${appointmentId} not found in readAppointments() result`);
      console.log('Available appointment IDs:', allAppointments.map(apt => `${apt.id} (${typeof apt.id})`).slice(0, 10));
      throw new Error('No appointment found with the given ID');
    }

    console.log(`✅ Found appointment: ${targetAppointment.nome} ${targetAppointment.cognome} (Current status: ${targetAppointment.status})`);

    // Extract the actual database ID (convert back from string if needed)
    let dbId = targetAppointment.id;
    if (typeof dbId === 'string' && !isNaN(parseInt(dbId, 10))) {
      dbId = parseInt(dbId, 10);
    }

    console.log(`Using database ID: ${dbId} (type: ${typeof dbId})`);

    // Use admin client for update operations (bypasses RLS)
    const { data, error } = await supabaseAdmin
      .from(TABLES.APPOINTMENTS)
      .update({
        status,
        updated_at: new Date().toISOString()
      })
      .eq('id', dbId)
      .select();

    if (error) {
      console.error('Supabase error during update appointment status:', error);

      // FALLBACK: Try with different ID formats if the first attempt failed
      console.log('🔄 Attempting fallback with different ID formats...');

      const fallbackIds = [
        String(appointmentId),
        parseInt(appointmentId, 10),
        appointmentId
      ].filter((id, index, arr) => arr.indexOf(id) === index); // Remove duplicates

      for (const fallbackId of fallbackIds) {
        if (fallbackId === dbId) continue; // Skip the one we already tried

        console.log(`Trying fallback ID: ${fallbackId} (type: ${typeof fallbackId})`);

        const { data: fallbackData, error: fallbackError } = await supabaseAdmin
          .from(TABLES.APPOINTMENTS)
          .update({
            status,
            updated_at: new Date().toISOString()
          })
          .eq('id', fallbackId)
          .select();

        if (!fallbackError && fallbackData && fallbackData.length > 0) {
          console.log('✅ Fallback update successful with ID:', fallbackId);
          return fallbackData[0];
        }
      }

      throw new Error(`Failed to update appointment: ${error.message}`);
    }

    console.log('Update result:', data);

    // Check if any rows were updated
    if (!data || data.length === 0) {
      console.log('⚠️ No rows updated, but no error returned. This might indicate an ID mismatch.');
      throw new Error('No appointment found with the given ID');
    }

    return data[0]; // Return the updated appointment
  } catch (error) {
    console.error('Error updating appointment status:', error);
    throw error; // Re-throw the error instead of returning false
  }
};

/**
 * Delete an appointment
 * @param {string} appointmentId - Appointment ID
 * @returns {boolean} Success status
 */
export const deleteAppointment = async (appointmentId) => {
  try {
    console.log(`deleteAppointment called with ID: ${appointmentId} (type: ${typeof appointmentId})`);

    // CRITICAL FIX: Use the same approach as readAppointments to find the appointment
    console.log('🔍 Finding appointment for deletion using same method as readAppointments...');

    // Get all appointments using the same method as the UI
    const allAppointments = await readAppointments();
    console.log(`Found ${allAppointments.length} total appointments from readAppointments()`);

    // Find the appointment by ID (handle both string and number)
    const targetAppointment = allAppointments.find(apt =>
      apt.id == appointmentId || apt.id === appointmentId ||
      apt.id === String(appointmentId) || apt.id === parseInt(appointmentId, 10)
    );

    if (!targetAppointment) {
      console.error(`Appointment with ID ${appointmentId} not found in readAppointments() result`);
      console.log('Available appointment IDs:', allAppointments.map(apt => `${apt.id} (${typeof apt.id})`).slice(0, 10));
      throw new Error('No appointment found with the given ID');
    }

    console.log(`✅ Found appointment for deletion: ${targetAppointment.nome} ${targetAppointment.cognome}`);

    // Extract the actual database ID (convert back from string if needed)
    let dbId = targetAppointment.id;
    if (typeof dbId === 'string' && !isNaN(parseInt(dbId, 10))) {
      dbId = parseInt(dbId, 10);
    }

    console.log(`Using database ID for deletion: ${dbId} (type: ${typeof dbId})`);

    // Use admin client for delete operations (bypasses RLS)
    const { data, error } = await supabaseAdmin
      .from(TABLES.APPOINTMENTS)
      .delete()
      .eq('id', dbId)
      .select();

    if (error) {
      console.error('Supabase error during delete appointment:', error);

      // FALLBACK: Try with different ID formats if the first attempt failed
      console.log('🔄 Attempting delete fallback with different ID formats...');

      const fallbackIds = [
        String(appointmentId),
        parseInt(appointmentId, 10),
        appointmentId
      ].filter((id, index, arr) => arr.indexOf(id) === index); // Remove duplicates

      for (const fallbackId of fallbackIds) {
        if (fallbackId === dbId) continue; // Skip the one we already tried

        console.log(`Trying delete fallback ID: ${fallbackId} (type: ${typeof fallbackId})`);

        const { data: fallbackData, error: fallbackError } = await supabaseAdmin
          .from(TABLES.APPOINTMENTS)
          .delete()
          .eq('id', fallbackId)
          .select();

        if (!fallbackError && fallbackData && fallbackData.length > 0) {
          console.log('✅ Fallback delete successful with ID:', fallbackId);
          return fallbackData[0];
        }
      }

      throw new Error(`Failed to delete appointment: ${error.message}`);
    }

    console.log('Delete result:', data);

    // Check if any rows were deleted
    if (!data || data.length === 0) {
      console.log('⚠️ No rows deleted, but no error returned. This might indicate an ID mismatch.');
      throw new Error('No appointment found with the given ID');
    }

    return data[0]; // Return the deleted appointment
  } catch (error) {
    console.error('Error deleting appointment:', error);
    throw error; // Re-throw the error instead of returning false
  }
};

/**
 * Get appointments by date range
 * @param {string} startDate - Start date (YYYY-MM-DD)
 * @param {string} endDate - End date (YYYY-MM-DD)
 * @returns {Array} Array of appointments in JSON format
 */
export const getAppointmentsByDateRange = async (startDate, endDate) => {
  try {
    const { data, error } = await supabase
      .from(TABLES.APPOINTMENTS)
      .select('*')
      .gte('data_appuntamento', startDate)
      .lte('data_appuntamento', endDate)
      .order('data_appuntamento', { ascending: true })
      .order('orario', { ascending: true });

    if (error) {
      handleSupabaseError(error, 'get appointments by date range');
    }

    // Convert to JSON format for API compatibility
    return data.map(convertSupabaseToJsonFormat);
  } catch (error) {
    console.error('Error getting appointments by date range:', error);
    return [];
  }
};

/**
 * Check if a time slot is available
 * @param {string} date - Date (YYYY-MM-DD)
 * @param {string} time - Time (HH:MM)
 * @returns {boolean} True if available, false if booked
 */
export const isTimeSlotAvailable = async (date, time) => {
  try {
    const { data, error } = await supabase
      .from(TABLES.APPOINTMENTS)
      .select('id')
      .eq('data_appuntamento', date)
      .eq('orario', time)
      .neq('status', APPOINTMENT_STATUS.CANCELLED)
      .limit(1);

    if (error) {
      console.error('Error checking time slot availability:', error);
      throw new Error(`Failed to check time slot availability: ${error.message}`);
    }

    return data.length === 0;
  } catch (error) {
    console.error('Error checking time slot availability:', error);
    return false;
  }
};

/**
 * Get appointments for a specific date
 * @param {string} date - Date (YYYY-MM-DD)
 * @returns {Array} Array of appointments in JSON format
 */
export const getAppointmentsByDate = async (date) => {
  return await getAppointmentsByDateRange(date, date);
};

/**
 * Get dashboard statistics
 * @returns {Object} Dashboard stats
 */
export const getDashboardStats = async () => {
  try {
    // Use the database function for better performance
    const { data, error } = await supabase
      .rpc('get_dashboard_stats');

    if (error) {
      handleSupabaseError(error, 'get dashboard stats');
    }

    return data || {
      total: 0,
      today: 0,
      thisWeek: 0,
      thisMonth: 0,
      byService: {},
      byStatus: {},
      recent: []
    };
  } catch (error) {
    console.error('Error getting dashboard stats:', error);
    
    // Fallback to manual calculation if the function fails
    try {
      const appointments = await readAppointments();
      const today = new Date().toISOString().split('T')[0];
      const thisWeek = getWeekRange(new Date());
      const thisMonth = getMonthRange(new Date());

      return {
        total: appointments.length,
        today: appointments.filter(app => app.dataAppuntamento === today).length,
        thisWeek: appointments.filter(app =>
          app.dataAppuntamento >= thisWeek.start &&
          app.dataAppuntamento <= thisWeek.end
        ).length,
        thisMonth: appointments.filter(app =>
          app.dataAppuntamento >= thisMonth.start &&
          app.dataAppuntamento <= thisMonth.end
        ).length,
        byService: getAppointmentsByService(appointments),
        byStatus: getAppointmentsByStatus(appointments),
        recent: appointments
          .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
          .slice(0, 5)
      };
    } catch (fallbackError) {
      console.error('Fallback stats calculation failed:', fallbackError);
      return {
        total: 0,
        today: 0,
        thisWeek: 0,
        thisMonth: 0,
        byService: {},
        byStatus: {},
        recent: []
      };
    }
  }
};

/**
 * Helper function to get appointments grouped by service
 * @param {Array} appointments - Array of appointments
 * @returns {Object} Appointments grouped by service
 */
const getAppointmentsByService = (appointments) => {
  return appointments.reduce((acc, appointment) => {
    const service = appointment.servizio;
    acc[service] = (acc[service] || 0) + 1;
    return acc;
  }, {});
};

/**
 * Helper function to get appointments grouped by status
 * @param {Array} appointments - Array of appointments
 * @returns {Object} Appointments grouped by status
 */
const getAppointmentsByStatus = (appointments) => {
  return appointments.reduce((acc, appointment) => {
    const status = appointment.status || 'confirmed';
    acc[status] = (acc[status] || 0) + 1;
    return acc;
  }, {});
};

/**
 * Helper function to get week range for a given date
 * @param {Date} date - Reference date
 * @returns {Object} Week start and end dates
 */
const getWeekRange = (date) => {
  const start = new Date(date);
  const day = start.getDay();
  const diff = start.getDate() - day + (day === 0 ? -6 : 1); // Adjust for Monday start
  start.setDate(diff);

  const end = new Date(start);
  end.setDate(start.getDate() + 6);

  return {
    start: start.toISOString().split('T')[0],
    end: end.toISOString().split('T')[0]
  };
};

/**
 * Helper function to get month range for a given date
 * @param {Date} date - Reference date
 * @returns {Object} Month start and end dates
 */
const getMonthRange = (date) => {
  const start = new Date(date.getFullYear(), date.getMonth(), 1);
  const end = new Date(date.getFullYear(), date.getMonth() + 1, 0);

  return {
    start: start.toISOString().split('T')[0],
    end: end.toISOString().split('T')[0]
  };
};
